/**
 * 微信SDK状态管理
 */
import { defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'
import { initWechatSDK } from '@/components/QrScanner/wechat'
import { isWechatBrowser, wechatSDK, WxSdkStatus } from '@/utils/wechat'

interface WechatState {
  isInitialized: boolean
  isInitializing: boolean
  initError: string
  lastInitUrl: string // 记录上次初始化的URL，用于判断是否需要重新初始化
}

export const useWechatStore = defineStore('wechat', () => {
  // 状态
  const state = ref<WechatState>({
    isInitialized: false,
    isInitializing: false,
    initError: '',
    lastInitUrl: '',
  })

  // 计算属性
  const isReady = computed(() => {
    return state.value.isInitialized && wechatSDK.getStatus() === WxSdkStatus.READY
  })

  const canScan = computed(() => {
    return isWechatBrowser() && isReady.value
  })

  // 显示状态
  const displayStatus = computed(() => {
    if (!isWechatBrowser()) {
      return 'not_wechat'
    }
    if (state.value.isInitializing) {
      return 'initializing'
    }
    if (state.value.initError) {
      return 'error'
    }
    if (isReady.value) {
      return 'ready'
    }
    return 'not_initialized'
  })

  // 获取当前页面URL（用于微信SDK配置）
  const getCurrentPageUrl = () => {
    if (typeof window === 'undefined')
      return ''
    return window.location.href.split('#')[0]
  }

  // 初始化微信SDK
  const initializeWechatSDK = async (force = false): Promise<boolean> => {
    // 如果不在微信环境中，直接返回
    if (!isWechatBrowser()) {
      console.log('不在微信环境中，跳过微信SDK初始化')
      return false
    }

    const currentUrl = getCurrentPageUrl()

    // 如果已经初始化且URL没有变化，且不是强制初始化，直接返回
    if (!force && state.value.isInitialized && state.value.lastInitUrl === currentUrl && isReady.value) {
      console.log('微信SDK已初始化，跳过重复初始化')
      return true
    }

    // 如果正在初始化，等待初始化完成
    if (state.value.isInitializing) {
      console.log('微信SDK正在初始化中，等待完成...')
      // 等待初始化完成
      while (state.value.isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return state.value.isInitialized
    }

    try {
      state.value.isInitializing = true
      state.value.initError = ''

      console.log('开始初始化微信SDK...', { currentUrl })

      // 获取微信配置
      const wxConfig = await initWechatSDK(false) // 生产环境设为false

      // 配置微信SDK
      await wechatSDK.configWx(wxConfig)

      state.value.isInitialized = true
      state.value.lastInitUrl = currentUrl
      state.value.initError = ''

      console.log('微信SDK初始化成功')
      return true
    }
    catch (error) {
      console.error('微信SDK初始化失败:', error)
      state.value.initError = String(error)
      state.value.isInitialized = false
      return false
    }
    finally {
      state.value.isInitializing = false
    }
  }

  // 重置状态
  const reset = () => {
    state.value.isInitialized = false
    state.value.isInitializing = false
    state.value.initError = ''
    state.value.lastInitUrl = ''
    wechatSDK.reset()
  }

  // 检查是否需要重新初始化（URL变化时）
  const checkAndReinitialize = async (): Promise<boolean> => {
    const currentUrl = getCurrentPageUrl()
    if (state.value.lastInitUrl && state.value.lastInitUrl !== currentUrl) {
      console.log('页面URL发生变化，重新初始化微信SDK', {
        lastUrl: state.value.lastInitUrl,
        currentUrl,
      })
      return await initializeWechatSDK(true)
    }
    return await initializeWechatSDK()
  }

  return {
    // 状态
    state: readonly(state),

    // 计算属性
    isReady,
    canScan,
    displayStatus,

    // 方法
    initializeWechatSDK,
    checkAndReinitialize,
    reset,
  }
})

// 导出便捷方法
export function useWechatSDK() {
  const store = useWechatStore()

  return {
    ...store,
    // 便捷的初始化方法
    ensureInitialized: () => store.initializeWechatSDK(),
    // 便捷的扫码检查方法
    canScan: store.canScan,
  }
}
