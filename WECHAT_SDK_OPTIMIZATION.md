# 微信SDK初始化优化方案

## 问题描述
微信SDK初始化完之后，状态需要存起来，现在切换首页和我的都会重新拉取。

## 问题分析
1. **当前状态**：微信SDK使用单例模式 (`wechatSDK`)，但状态没有持久化存储
2. **问题现象**：每次切换页面（首页和我的页面）时，QrScanner组件都会重新初始化微信SDK
3. **根本原因**：
   - QrScanner组件在每个页面的 `onMounted` 钩子中都会调用 `initWechat()`
   - 微信SDK的状态只存在内存中，没有持久化
   - 页面切换时组件重新挂载，导致重复初始化

## 解决方案

### 1. 微信SDK状态管理优化 (`src/utils/wechat.ts`)
- 添加 `initPromise` 属性，避免重复初始化
- 修改 `configWx` 方法，增加去重逻辑：
  - 如果已经初始化完成且状态正常，直接返回
  - 如果正在初始化，等待初始化完成
  - 只有在未初始化或失败时才开始新的初始化

### 2. 全局状态管理 (`src/store/wechat.ts`)
- 使用 Pinia 创建微信SDK状态管理store
- 状态包括：
  - `isInitialized`: 是否已初始化
  - `isInitializing`: 是否正在初始化
  - `initError`: 初始化错误信息
  - `lastInitUrl`: 上次初始化的URL（用于检测URL变化）
- 提供方法：
  - `initializeWechatSDK()`: 初始化微信SDK
  - `checkAndReinitialize()`: 检查并重新初始化（URL变化时）
  - `reset()`: 重置状态

### 3. 组件优化 (`src/components/QrScanner/index.vue`)
- 移除本地的 `wxInitialized` 和 `wxInitError` 状态
- 使用全局的 `useWechatSDK()` store
- 简化初始化逻辑，直接调用 `wechatStore.ensureInitialized()`

### 4. 应用级初始化 (`src/modules/wechat.ts`)
- 创建微信SDK初始化插件
- 在应用启动时自动初始化微信SDK（如果在微信环境中）

### 5. 路由守卫 (`src/router/guard/wechatGuard.ts`)
- 创建微信SDK路由守卫
- 在页面切换时检查并重新初始化微信SDK（如果URL变化）
- 确保微信SDK状态在路由变化时保持正确

## 核心改进

### 避免重复初始化
```typescript
// 之前：每次组件挂载都会初始化
onMounted(() => {
  if (isInWechat.value) {
    initWechat() // 每次都会重新初始化
  }
})

// 现在：使用全局状态管理，避免重复初始化
onMounted(() => {
  wechatStore.ensureInitialized() // 只在需要时初始化
})
```

### 状态持久化
```typescript
// 全局状态管理，跨组件共享
const wechatStore = useWechatStore()

// 检查是否已初始化
if (wechatStore.isReady) {
  // 直接使用，无需重新初始化
}
```

### URL变化检测
```typescript
// 检测URL变化，重新初始化（微信SDK需要根据URL重新配置）
const checkAndReinitialize = async () => {
  const currentUrl = getCurrentPageUrl()
  if (state.value.lastInitUrl && state.value.lastInitUrl !== currentUrl) {
    return await initializeWechatSDK(true) // 强制重新初始化
  }
  return await initializeWechatSDK() // 正常初始化检查
}
```

## 使用方式

### 在组件中使用
```typescript
import { useWechatSDK } from '@/store/wechat'

const wechatStore = useWechatSDK()

// 确保初始化
await wechatStore.ensureInitialized()

// 检查是否可以扫码
if (wechatStore.canScan) {
  // 执行扫码操作
}
```

### 状态检查
```typescript
// 检查初始化状态
console.log('是否已初始化:', wechatStore.isReady)
console.log('是否正在初始化:', wechatStore.state.isInitializing)
console.log('初始化错误:', wechatStore.state.initError)
```

## 预期效果
1. **避免重复初始化**：页面切换时不会重复调用微信SDK初始化
2. **状态共享**：所有组件共享同一个微信SDK状态
3. **智能重新初始化**：只在URL变化时重新初始化
4. **更好的用户体验**：减少不必要的网络请求和初始化时间
5. **错误处理**：统一的错误处理和状态管理

## 文件变更清单
- `src/utils/wechat.ts` - 微信SDK核心逻辑优化
- `src/store/wechat.ts` - 新增全局状态管理
- `src/components/QrScanner/index.vue` - 组件逻辑简化
- `src/modules/wechat.ts` - 新增应用级初始化插件
- `src/router/guard/wechatGuard.ts` - 新增路由守卫
- `src/router/index.ts` - 注册路由守卫
